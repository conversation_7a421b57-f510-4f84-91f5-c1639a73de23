{"name": "membership-module", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/src/main.js", "dev": "docker compose up", "build": "node build.js && tsc --build tsconfig.json && tsc-alias", "nodemon": "nodemon --config nodemon.debug.json", "test": "echo \"Error: no test specified\" && exit 1", "migrate:latest": "npx knex migrate:latest --env production", "format": "prettier --write 'src/**/*.{js,ts}'", "lint": "eslint . --ext .js,.ts,.jsx,.tsx"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@cbidigital/aqua-ddd": "^1.0.0-rc.9", "@heronjs/common": "3.3.40", "@heronjs/core": "3.4.40", "@heronjs/express": "^3.1.7", "axios": "^1.9.0", "class-validator": "^0.14.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "pg": "^8.15.6", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "stripe": "^18.1.0", "tsc-alias": "^1.8.15", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "fs-extra": "^11.3.0", "globals": "^16.2.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0"}}