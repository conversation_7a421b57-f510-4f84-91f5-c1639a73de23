import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  IDatabaseUtil,
  MembershipCycleEnum,
  IMembershipBuilder,
  UpdateMembershipInput,
  IMembershipRepository,
  MembershipNotFoundError,
} from "@features/domain";

export type UpdateMembershipUseCaseInput = UpdateMembershipInput;
export type UpdateMembershipUseCaseOutput = { id: string };

const UpdateMembershipUseCaseInputSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  isCustom: z.boolean().optional(),
  isTrial: z.boolean().optional(),
  description: z.string().optional(),
  cycles: z
    .array(
      z.object({
        id: z.string().optional(),
        amount: z.number().optional(),
        billingCycle: z.nativeEnum(MembershipCycleEnum).optional(),
      }),
    )
    .optional(),
  capabilities: z
    .array(
      z.object({
        id: z.string().optional(),
        value: z.string().optional(),
        capabilityId: z.string().optional(),
      }),
    )
    .optional(),
});

export type IUpdateMembershipUseCase = IUseCase<
  UpdateMembershipUseCaseInput,
  UpdateMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.UPDATE_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class UpdateMembershipUseCase
  extends UseCase<UpdateMembershipUseCaseInput, UpdateMembershipUseCaseOutput, UseCaseContext>
  implements IUpdateMembershipUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.MEMBERSHIP)
    protected readonly builder: IMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly repo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: UpdateMembershipUseCaseInput) => {
    const authId = this.context.auth?.authId!;
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const model = UpdateMembershipUseCaseInputSchema.parse(input);
      const membership = await this.repo.findOne(
        { filter: { id: { $eq: model.id } } },
        repoOptions,
      );
      if (!membership) throw new MembershipNotFoundError();
      await membership.update(model, { auth: { authId } });
      await this.repo.update(membership, repoOptions);
      await trx.commit();
      return { id: membership.id };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  };
}
