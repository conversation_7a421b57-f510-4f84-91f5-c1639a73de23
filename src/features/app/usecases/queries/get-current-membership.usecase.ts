import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ITenantMembershipDao } from "@features/infra";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  TenantMembershipDto,
  ITenantMembershipMapper,
  TenantMembershipNotFoundError,
} from "@features/domain";

export type GetCurrentMembershipUseCaseInput = {
  tenantId: string;
};

export type GetCurrentMembershipUseCaseOutput = TenantMembershipDto;

const GetCurrentMembershipUseCaseInputSchema = z.object({
  tenantId: z.string(),
});

export type IGetCurrentMembershipUseCase = IUseCase<
  GetCurrentMembershipUseCaseInput,
  GetCurrentMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_CURRENT_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class GetCurrentMembershipUseCase
  extends UseCase<
    GetCurrentMembershipUseCaseInput,
    GetCurrentMembershipUseCaseOutput,
    UseCaseContext
  >
  implements IGetCurrentMembershipUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP)
    protected readonly dao: ITenantMembershipDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP)
    protected readonly mapper: ITenantMembershipMapper,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetCurrentMembershipUseCaseInput) => {
    const { tenantId } = GetCurrentMembershipUseCaseInputSchema.parse(input);

    // Find active tenant membership for the current tenant
    const dto = await this.dao.findOne({
      filter: {
        tenantId: { $eq: tenantId },
      },
    });

    if (!dto) throw new TenantMembershipNotFoundError();

    return dto;
  };
}
