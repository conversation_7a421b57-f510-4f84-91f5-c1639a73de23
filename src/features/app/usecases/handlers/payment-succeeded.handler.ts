import {
  Subscription,
  IDatabaseUtil,
  IPaymentGateway,
  ITenantMembership,
  IMembershipRepository,
  PaymentSucceededEvent,
  SubscriptionFoundError,
  IPaymentGatewayFactory,
  BillingCycleStatusEnum,
  ITenantMembershipRepository,
  TenantMembershipNotFoundError,
  MissingTenantMembershipIdError,
} from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { RepositoryOptions } from "@cbidigital/aqua-ddd";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHandler } from "@features/app/usecases/handlers/webhook-handler.factory";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.HANDLER.PAYMENT_SUCCEEDED,
  scope: Lifecycle.Singleton,
})
export class PaymentSucceededHandler implements IWebhookHandler {
  private readonly logger: ILogger;
  private paymentGateway: IPaymentGateway;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: PaymentSucceededEvent) {
    const { data } = event;
    const invoiceId = data.invoice;
    const { tenantMembershipId } = data.metadata;
    if (!tenantMembershipId) throw new MissingTenantMembershipIdError();
    this.logger.info("Handled payment succeeded for:::", tenantMembershipId);
    this.paymentGateway = this.paymentGatewayFactory.get(event.gateway);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      // Handle subscription created
      const { tenantMembership, subscription } = await this.fetchSubscription(event, repoOptions);
      await this.updateBillingCycle(tenantMembership, subscription);
      await tenantMembership.activate();
      await this.tenantMembershipRepo.update(tenantMembership, repoOptions);
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle payment succeeded", error);
      throw error;
    }
  }

  private async fetchSubscription(event: PaymentSucceededEvent, repoOptions: RepositoryOptions) {
    const { tenantMembershipId } = event.data.metadata;
    const [tenantMembership, subscription] = await Promise.all([
      this.tenantMembershipRepo.findOne(
        { filter: { id: { $eq: tenantMembershipId } } },
        repoOptions,
      ),
      this.paymentGateway.getSubscription(event.data.subscription),
    ]);
    if (!tenantMembership) throw new TenantMembershipNotFoundError();
    if (!subscription) throw new SubscriptionFoundError();
    return { tenantMembership, subscription };
  }

  private async updateBillingCycle(
    tenantMembership: ITenantMembership,
    subscription: Subscription,
  ) {
    const pendingBillingCycle = tenantMembership.getPendingBillingCycle();
    const { periodStart, periodEnd } = subscription;
    if (pendingBillingCycle) {
      await pendingBillingCycle.update({
        periodStart,
        periodEnd,
        status: BillingCycleStatusEnum.SUCCEEDED,
      });
    }
  }
}
