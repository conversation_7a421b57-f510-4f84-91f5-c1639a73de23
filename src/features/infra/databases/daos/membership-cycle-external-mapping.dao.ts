import { <PERSON><PERSON> } from "knex";
import { MembershipCycleExternalMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { MembershipCycleExternalMappingRecord } from "@features/infra/databases/records";
import { MembershipCycleExternalMappingRecordMapper } from "@features/infra/databases/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS, MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";

export interface IMembershipCycleExternalMappingDao
  extends IBaseDao<MembershipCycleExternalMappingDto, MembershipCycleExternalMappingRecord> {}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipCycleExternalMappingDao
  extends BaseDao<MembershipCycleExternalMappingDto, MembershipCycleExternalMappingRecord>
  implements IMembershipCycleExternalMappingDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING,
      recordMapper: new MembershipCycleExternalMappingRecordMapper(),
    });
  }

  async create(
    dto: MembershipCycleExternalMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipCycleExternalMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select("*");
    return query;
  }
}
