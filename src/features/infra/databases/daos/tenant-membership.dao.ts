import { <PERSON><PERSON> } from "knex";
import { TenantMembershipDto } from "@features/domain";
import { TenantMembershipRecord } from "@features/infra/databases/records";
import { Dao, DataSource, Lifecycle, Logger, Optional } from "@heronjs/common";
import { TenantMembershipQueryConfig } from "@features/infra/databases/query-configs";
import { TenantMembershipRecordMapper } from "@features/infra/databases/record-mappers";
import {
  BaseDao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInput,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_TABLE_NAMES,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
} from "@constants";

export interface ITenantMembershipDao
  extends IBaseDao<TenantMembershipDto, TenantMembershipRecord> {
  update(
    entity: Partial<TenantMembershipDto>,
    options?: RepositoryOptions,
  ): Promise<Partial<TenantMembershipDto>>;
}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipDao
  extends BaseDao<TenantMembershipDto, TenantMembershipRecord>
  implements ITenantMembershipDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP,
      recordMapper: new TenantMembershipRecordMapper(),
    });
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    builder.select(
      `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP}.*`,
      builder.client.raw(
        `json_agg(${MEMBERSHIP_MODULE_TABLE_NAMES.BILLING_CYCLE}.*)
        FILTER (WHERE ${MEMBERSHIP_MODULE_TABLE_NAMES.BILLING_CYCLE}.id IS NOT NULL) as billing_cycles`,
      ),
      builder.client.raw(
        `json_agg(DISTINCT ${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING}.*)
        FILTER (WHERE ${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING}.id IS NOT NULL) as external_mappings`,
      ),
      builder.client.raw(
        `json_build_object(
            'id', ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.id,
            'membership_id', ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.membership_id,
            'billing_cycle', ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.billing_cycle,
            'amount', ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.amount,
            'created_at', ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.created_at,
            'updated_at', ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.updated_at
          ) as membership_cycle`,
      ),
    );
  }

  private buildJoinClause(builder: Knex.QueryBuilder) {
    builder
      .leftJoin(
        MEMBERSHIP_MODULE_TABLE_NAMES.BILLING_CYCLE,
        `${MEMBERSHIP_MODULE_TABLE_NAMES.BILLING_CYCLE}.${MEMBERSHIP_MODULE_COLUMN_NAMES.BILLING_CYCLE.TENANT_MEMBERSHIP_ID}`,
        `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.ID}`,
      )
      .leftJoin(
        MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE,
        `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CYCLE.ID}`,
        `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.MEMBERSHIP_CYCLE_ID}`,
      )
      .leftJoin(
        MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING,
        `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING.TENANT_MEMBERSHIP_ID}`,
        `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.ID}`,
      );
  }

  private buildGroupByClause(builder: Knex.QueryBuilder) {
    builder.groupBy(
      `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CYCLE.ID}`,
    );
  }

  private applySearch(query: Knex.QueryBuilder, search: string) {
    return query.where((builder) => {
      builder.where(
        MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.TENANT_ID,
        "like",
        `%${search}%`,
      );
    });
  }

  async find(
    payload: QueryInput,
    options?: RepositoryOptions,
  ): Promise<Partial<TenantMembershipDto>[]> {
    const { offset, limit, sort, filter, search } = payload;
    const client = this.db.getClient(options?.tenantId);
    const query = client.from(this.tableName).modify(this.buildSelectClause);
    if (search) this.applySearch(query, search);
    if (filter) DaoUtils.applyFilter(filter, TenantMembershipQueryConfig, query);
    if (offset !== undefined) query.offset(offset);
    if (limit !== undefined) query.limit(limit);
    if (sort) DaoUtils.applySort(sort, TenantMembershipQueryConfig, query);
    const records = await query;
    const dtos = this.recordMapper.fromRecordsToDtos(records);
    return dtos;
  }

  async findOne(
    payload?: QueryInputFindOne<TenantMembershipDto>,
    options?: RepositoryOptions,
  ): Promise<Optional<Partial<TenantMembershipDto>>> {
    const client = this.db.getClient(options?.tenantId);
    const query = client
      .from(this.tableName)
      .modify(this.buildSelectClause)
      .modify(this.buildJoinClause)
      .modify(this.buildGroupByClause);
    // .modify(this.buildHavingClause);
    if (payload?.filter) DaoUtils.applyFilter(payload.filter, TenantMembershipQueryConfig, query);
    if (options?.trx) query.transacting(options.trx);
    const record = await query.first();
    return record ? this.recordMapper.fromRecordToDto(record) : undefined;
  }

  async update(
    dto: Partial<TenantMembershipDto>,
    options: RepositoryOptions,
  ): Promise<Partial<TenantMembershipDto>> {
    const client = this.db.getClient(options?.tenantId);
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client
      .table(this.tableName)
      .where(MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.ID, dto.id!)
      .update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
