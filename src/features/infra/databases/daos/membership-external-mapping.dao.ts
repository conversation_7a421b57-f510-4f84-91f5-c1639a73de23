import { <PERSON><PERSON> } from "knex";
import { MembershipExternalMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { MembershipExternalMappingRecord } from "@features/infra/databases/records";
import { MembershipExternalMappingRecordMapper } from "@features/infra/databases/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS, MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";

export interface IMembershipExternalMappingDao
  extends IBaseDao<MembershipExternalMappingDto, MembershipExternalMappingRecord> {}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipExternalMappingDao
  extends BaseDao<MembershipExternalMappingDto, MembershipExternalMappingRecord>
  implements IMembershipExternalMappingDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_EXTERNAL_MAPPING,
      recordMapper: new MembershipExternalMappingRecordMapper(),
    });
  }

  async create(
    dto: MembershipExternalMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipExternalMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select("*");
    return query;
  }

  private buildJoinClause(builder: Knex.QueryBuilder) {
    // const query = builder.leftJoin(
    //     INVENTORY_TABLE_NAMES.ITEM,
    //     `${INVENTORY_TABLE_NAMES.PURCHASE_ORDER_ITEM}.${COLUMN_NAMES.PURCHASE_ORDER_ITEM.ITEM_ID}`,
    //     `${INVENTORY_TABLE_NAMES.ITEM}.${COLUMN_NAMES.ITEM.ID}`,
    // );
    // return query;
  }

  private applySearch(builder: Knex.QueryBuilder, search: string) {
    const query = builder.where((builder) => {
      builder.whereILike(`${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.name`, `%${search}%`);
    });
    return query;
  }

  //   async find(
  //     payload: QueryInput & { search?: string },
  //     options?: RepositoryOptions
  //   ): Promise<Partial<MembershipCycleDto>[]> {
  //     const { offset, limit, sort, filter, search } = payload;
  //     const client = this.db.getClient(options?.tenantId);
  //     const query = client.from(this.tableName).modify(this.buildSelectClause);
  //     if (search) this.applySearch(query, search);
  //     if (filter) DaoUtils.applyFilter(filter, MembershipCycleQueryConfig, query);
  //     if (offset !== undefined) query.offset(offset);
  //     if (limit !== undefined) query.limit(limit);
  //     if (sort) DaoUtils.applySort(sort, MembershipCycleQueryConfig, query);
  //     const records = await query;
  //     const dtos = this.recordMapper.fromRecordsToDtos(records);
  //     return dtos;
  //   }

  //   async count(
  //     payload: QueryInput & { search?: string },
  //     options?: RepositoryOptions
  //   ): Promise<number> {
  //     const { filter, search } = payload;
  //     const client = this.db.getClient(options?.tenantId);
  //     const query = client.count("*").from(this.tableName);
  //     if (search) this.applySearch(query, search);
  //     if (filter) DaoUtils.applyFilter(filter, MembershipCycleQueryConfig, query);
  //     const { count } = await query.first();
  //     return +count;
  //   }

  //   async upsertList(
  //     dtos: Partial<MembershipCycleDto>[],
  //     options?: RepositoryOptions
  //   ) {
  //     const client = this.db.getClient();
  //     const conflicColumns = ["id"];
  //     const records = this.recordMapper.fromDtosToRecords(dtos);
  //     const query = client
  //       .table(this.tableName)
  //       .insert(records)
  //       .onConflict(conflicColumns)
  //       .merge(this.mergedColumns);
  //     if (options?.trx) query.transacting(options.trx);
  //     await query;
  //     return dtos;
  //   }
}
