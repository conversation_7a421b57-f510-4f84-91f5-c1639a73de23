import { <PERSON><PERSON> } from "knex";
import { TenantMembershipExternalMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger, Optional } from "@heronjs/common";
import { TenantMembershipExternalMappingRecord } from "@features/infra/databases/records";
import { TenantMembershipExternalMappingQueryConfig } from "@features/infra/databases/query-configs";
import { TenantMembershipExternalMappingRecordMapper } from "@features/infra/databases/record-mappers";
import {
  BaseDao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
  MEMBERSHIP_MODULE_TABLE_NAMES,
} from "@constants";

export interface ITenantMembershipExternalMappingDao
  extends IBaseDao<TenantMembershipExternalMappingDto, TenantMembershipExternalMappingRecord> {}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipExternalMappingDao
  extends BaseDao<TenantMembershipExternalMappingDto, TenantMembershipExternalMappingRecord>
  implements ITenantMembershipExternalMappingDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING,
      recordMapper: new TenantMembershipExternalMappingRecordMapper(),
    });
  }

  async create(
    dto: TenantMembershipExternalMappingDto,
    options?: RepositoryOptions,
  ): Promise<TenantMembershipExternalMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select("*");
    return query;
  }

  private buildJoinClause(builder: Knex.QueryBuilder) {
    builder.leftJoin(
      MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING.TENANT_MEMBERSHIP_ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.ID}`,
    );
  }

  private applySearch(builder: Knex.QueryBuilder, search: string) {
    const query = builder.where((builder) => {
      builder.whereILike(`${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.name`, `%${search}%`);
    });
    return query;
  }

  async findOne(
    payload?: QueryInputFindOne<TenantMembershipExternalMappingDto>,
    options?: RepositoryOptions,
  ): Promise<Optional<Partial<TenantMembershipExternalMappingDto>>> {
    const client = this.db.getClient(options?.tenantId);
    const query = client
      .from(this.tableName)
      .modify(this.buildSelectClause)
      .modify(this.buildJoinClause);
    if (payload?.filter)
      DaoUtils.applyFilter(payload.filter, TenantMembershipExternalMappingQueryConfig, query);
    if (options?.trx) query.transacting(options.trx);
    const record = await query.first();
    if (!record) return undefined;
    return this.recordMapper.fromRecordToDto(record);
  }

  //   async find(
  //     payload: QueryInput & { search?: string },
  //     options?: RepositoryOptions
  //   ): Promise<Partial<MembershipCycleDto>[]> {
  //     const { offset, limit, sort, filter, search } = payload;
  //     const client = this.db.getClient(options?.tenantId);
  //     const query = client.from(this.tableName).modify(this.buildSelectClause);
  //     if (search) this.applySearch(query, search);
  //     if (filter) DaoUtils.applyFilter(filter, MembershipCycleQueryConfig, query);
  //     if (offset !== undefined) query.offset(offset);
  //     if (limit !== undefined) query.limit(limit);
  //     if (sort) DaoUtils.applySort(sort, MembershipCycleQueryConfig, query);
  //     const records = await query;
  //     const dtos = this.recordMapper.fromRecordsToDtos(records);
  //     return dtos;
  //   }

  //   async count(
  //     payload: QueryInput & { search?: string },
  //     options?: RepositoryOptions
  //   ): Promise<number> {
  //     const { filter, search } = payload;
  //     const client = this.db.getClient(options?.tenantId);
  //     const query = client.count("*").from(this.tableName);
  //     if (search) this.applySearch(query, search);
  //     if (filter) DaoUtils.applyFilter(filter, MembershipCycleQueryConfig, query);
  //     const { count } = await query.first();
  //     return +count;
  //   }

  //   async upsertList(
  //     dtos: Partial<MembershipCycleDto>[],
  //     options?: RepositoryOptions
  //   ) {
  //     const client = this.db.getClient();
  //     const conflicColumns = ["id"];
  //     const records = this.recordMapper.fromDtosToRecords(dtos);
  //     const query = client
  //       .table(this.tableName)
  //       .insert(records)
  //       .onConflict(conflicColumns)
  //       .merge(this.mergedColumns);
  //     if (options?.trx) query.transacting(options.trx);
  //     await query;
  //     return dtos;
  //   }
}
