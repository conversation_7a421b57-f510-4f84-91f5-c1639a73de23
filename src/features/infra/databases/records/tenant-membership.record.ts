import { Nullable } from "@heronjs/common";
import { PaymentGatewayProvider } from "@features/domain";
import { BillingCycleRecord } from "./billing-cycle.record";
import { TenantMembershipStatusEnum } from "@features/domain/aggregates/tenant-memberships/enums";
import { MembershipCycleRecord } from "@features/infra/databases/records/membership-cycle.record";
import { TenantMembershipExternalMappingRecord } from "@features/infra/databases/records/tenant-membership-external-mapping.record";

export type TenantMembershipRecord = {
  id: string;
  tenant_id: string;
  membership_id: string;
  membership_cycle_id: string;
  payment_method_id: Nullable<string>;
  status: TenantMembershipStatusEnum;
  gateway: Nullable<PaymentGatewayProvider>;
  trial_end: Nullable<number>;
  created_at: Date;
  created_by: string;
  updated_at: Nullable<Date>;
  updated_by: Nullable<string>;
  billing_cycles?: BillingCycleRecord[];
  membership_cycle?: MembershipCycleRecord;
  external_mappings?: TenantMembershipExternalMappingRecord[];
};
