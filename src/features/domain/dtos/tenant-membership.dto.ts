import { Nullable } from "@heronjs/common";
import { BillingCycleDto } from "./billing-cycle.dto";
import { PaymentGatewayProvider } from "@features/domain/services";
import { MembershipCycleDto } from "@features/domain/dtos/membership-cycle.dto";
import { TenantMembershipStatusEnum } from "@features/domain/aggregates/tenant-memberships/enums";
import { TenantMembershipExternalMappingDto } from "@features/domain/dtos/tenant-membership-external-mapping.dto";

export type TenantMembershipDto = {
  id: string;
  tenantId: string;
  membershipId: string;
  membershipCycleId: string;
  paymentMethodId: Nullable<string>;
  status: TenantMembershipStatusEnum;
  trialEnd: Nullable<number>;
  gateway: Nullable<PaymentGatewayProvider>;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  billingCycles: BillingCycleDto[];
  membershipCycle?: MembershipCycleDto;
  externalTenantMembershipMappings?: TenantMembershipExternalMappingDto[];
};
